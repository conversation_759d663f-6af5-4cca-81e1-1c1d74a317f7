Multi-Language Support Implementation Plan
Overview
We'll implement next-intl for internationalization in your Next.js 15 application with Supabase SSR. The implementation will use the App Router with i18n routing pattern, supporting multiple languages with locale-based URLs.

Scratchpad - Tasks to Execute
Phase 1: Setup and Configuration
Install next-intl package
Install next-intl using pnpm
Update next.config.ts with next-intl plugin
Create i18n configuration structure
Create src/i18n/routing.ts - Define supported locales and routing config
Create src/i18n/request.ts - Configure server-side i18n
Create src/i18n/navigation.ts - Setup locale-aware navigation APIs
Create message files
Create messages/ directory in project root
Create messages/en.json (default English)
Create messages/es.json (Spanish example)
Structure messages by component/page namespaces
Phase 2: App Structure Refactoring
Restructure app directory for locale routing
Move current  src/app/layout.tsx to src/app/[locale]/layout.tsx
Update layout to handle locale parameter and validation
Move all current pages under src/app/[locale]/ structure
Update middleware to integrate with next-intl routing
Update middleware integration
Modify existing  src/middleware.ts to work with next-intl
Ensure Supabase auth middleware works with i18n routing
Handle locale detection and redirection
Phase 3: Component Translation
Update core navigation components
Translate Sidebar.tsx - app name, navigation items
Translate TopBar.tsx - status messages, tooltips
Translate MenuItem.tsx - menu labels
Update navigation data structure to support translations
Update authentication components
Translate user-auth-form.tsx - form labels, buttons, messages
Translate auth pages (login, signup, callback)
Update error messages and validation text
Update dashboard components
Translate dashboard layout and common components
Update page titles and metadata
Translate form components and validation messages
Phase 4: Server Components and Actions
Update server components
Use getTranslations from next-intl/server in server components
Update metadata generation for SEO
Translate server-side error messages
Update server actions
Translate server action responses and error messages
Update form validation messages
Ensure proper locale context in actions
Phase 5: Client Components and Hooks
Update client components
Use useTranslations hook in client components
Update dynamic content and user feedback messages
Translate loading states and interactive elements
Create language switcher
Build locale switcher component
Integrate with existing UI design
Handle locale changes and persistence
Phase 6: Advanced Features
Date and number formatting
Configure locale-specific date formatting
Setup number and currency formatting
Handle timezone considerations
Type safety and validation
Setup TypeScript integration for message keys
Configure strict typing for translations
Add development tools for missing translations
Phase 7: Testing and Optimization
Testing and validation
Test all routes with different locales
Verify server-side rendering works correctly
Test middleware integration with auth flows
Validate static generation capabilities
Performance optimization
Implement message splitting for large apps
Configure proper caching strategies
Optimize bundle size for translations
Implementation Details
Supported Locales
Primary: English (en) - Default locale
Secondary: Spanish (es) - Example additional locale
Extensible: Easy to add more locales later
URL Structure
/en/dashboard - English dashboard
/es/dashboard - Spanish dashboard
/ - Redirects to default locale (/en)
Translation Organization
Messages will be organized by:

Component namespaces: Sidebar, TopBar, AuthForm
Page namespaces: Dashboard, Login, Profile
Common namespaces: Common, Errors, Validation
Integration Points
Middleware: Integrate i18n routing with existing Supabase auth middleware
Server Components: Use getTranslations for server-side translations
Client Components: Use useTranslations hook for client-side translations
Server Actions: Translate server action responses and validation
Metadata: Localize page titles, descriptions, and SEO content
Key Considerations
Existing Middleware: Carefully integrate with current Supabase auth middleware
RBAC System: Ensure role-based access control works with localized routes
Organization Context: Maintain organization switching functionality
Performance: Minimize bundle size impact and maintain SSR performance
Type Safety: Leverage TypeScript for translation key validation
This plan provides a systematic approach to implementing multi-language support while preserving all existing functionality. Each phase builds upon the previous one, ensuring a smooth transition to the internationalized application.