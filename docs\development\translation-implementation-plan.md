# Multi-Language Support Implementation Plan

## Overview
We'll implement next-intl for internationalization in your Next.js 15 application with Supabase SSR. The implementation will use a **hybrid approach** without changing URL structure, using localStorage for user preferences and Zustand for runtime performance.

## Translation Storage Strategy

### **Hybrid Approach (Recommended)**
- **Server-side JSON files** for initial load and SEO
- **Client-side Zustand store** for runtime performance
- **localStorage** for user preference persistence
- **No URL structure changes** - same routes for all languages

### **Benefits:**
- ✅ SEO-friendly (server-side rendering with translations)
- ✅ Fast language switching (client-side state)
- ✅ Persistent user preferences (localStorage)
- ✅ Optimal performance (hybrid loading)
- ✅ No URL complexity (same routes)

## Scratchpad - Tasks to Execute

### Phase 1: Setup and Configuration
1. **Install next-intl package** ✅ COMPLETED
   - ✅ Install `next-intl` using pnpm
   - ✅ Configure next.config.ts with next-intl plugin (without routing)

2. **Create i18n configuration structure** ✅ COMPLETED
   - ✅ Create `src/i18n/request.ts` - Configure server-side i18n without routing
   - ✅ Create `src/lib/i18n/locale-store.ts` - Zustand store for client-side locale management
   - ✅ Create `src/lib/i18n/locale-utils.ts` - Utility functions for locale handling

3. **Create message files** ✅ COMPLETED
   - ✅ Create `messages/` directory in project root
   - ✅ Create `messages/en.json` (default English)
   - ✅ Create `messages/nl.json` (Dutch)
   - ✅ Create `messages/th.json` (Thai)
   - ✅ Structure messages by component/page namespaces

### Phase 2: State Management Setup
4. **Create locale management system** ✅ COMPLETED
   - ✅ Build Zustand store for current locale state
   - ✅ Implement localStorage persistence
   - ✅ Create locale detection logic (localStorage → browser → default)
   - ✅ Add locale change handlers

5. **Update root layout** ✅ COMPLETED
   - ✅ Integrate NextIntlClientProvider in existing layout
   - ✅ Add locale detection and hydration logic
   - ✅ Ensure no URL structure changes
   - ✅ Handle server-client locale synchronization
### Phase 3: Component Translation
6. **Update core navigation components**
   - Translate `Sidebar.tsx` - app name, navigation items
   - Translate `TopBar.tsx` - status messages, tooltips
   - Translate `MenuItem.tsx` - menu labels
   - Update navigation data structure to support translations

7. **Update authentication components**
   - Translate `user-auth-form.tsx` - form labels, buttons, messages
   - Translate auth pages (login, signup, callback)
   - Update error messages and validation text

8. **Update dashboard components**
   - Translate dashboard layout and common components
   - Update page titles and metadata
   - Translate form components and validation messages

### Phase 4: Server Components and Actions
9. **Update server components**
   - Use `getTranslations` from `next-intl/server` in server components
   - Update metadata generation for SEO
   - Translate server-side error messages

10. **Update server actions**
    - Translate server action responses and error messages
    - Update form validation messages
    - Ensure proper locale context in actions

### Phase 5: Client Components and Hooks
11. **Update client components**
    - Use `useTranslations` hook in client components
    - Update dynamic content and user feedback messages
    - Translate loading states and interactive elements

12. **Create language switcher**
    - Build locale switcher component
    - Integrate with existing UI design
    - Handle locale changes and persistence
### Phase 6: Advanced Features
13. **Date and number formatting**
    - Configure locale-specific date formatting
    - Setup number and currency formatting
    - Handle timezone considerations

14. **Type safety and validation**
    - Setup TypeScript integration for message keys
    - Configure strict typing for translations
    - Add development tools for missing translations

### Phase 7: Testing and Optimization
15. **Testing and validation**
    - Test all routes with different locales
    - Verify server-side rendering works correctly
    - Test locale switching functionality
    - Validate localStorage persistence

16. **Performance optimization**
    - Implement message splitting for large apps
    - Configure proper caching strategies
    - Optimize bundle size for translations

## Implementation Details

### Supported Locales
- **Primary**: English (en) - Default locale
- **Secondary**: Dutch (nl) - Netherlands
- **Tertiary**: Thai (th) - Thailand
- **Extensible**: Easy to add more locales later

### URL Structure (No Changes)
- `/dashboard` - Works for all languages
- `/auth/login` - Works for all languages
- **No locale prefixes in URLs**
- Language preference stored in localStorage + Zustand

### Translation Organization
Messages will be organized by:
- **Component namespaces**: `Sidebar`, `TopBar`, `AuthForm`
- **Page namespaces**: `Dashboard`, `Login`, `Profile`
- **Common namespaces**: `Common`, `Errors`, `Validation`

### Integration Points
- **Server Components**: Use `getTranslations` for server-side translations
- **Client Components**: Use `useTranslations` hook for client-side translations
- **Server Actions**: Translate server action responses and validation
- **Metadata**: Localize page titles, descriptions, and SEO content
- **State Management**: Zustand store for runtime locale management

### Key Considerations
1. **No URL Changes**: Maintain existing routing structure
2. **RBAC System**: Ensure role-based access control works with translations
3. **Organization Context**: Maintain organization switching functionality
4. **Performance**: Minimize bundle size impact and maintain SSR performance
5. **Type Safety**: Leverage TypeScript for translation key validation
6. **User Experience**: Instant language switching without page reloads

This plan provides a systematic approach to implementing multi-language support while preserving all existing functionality and URL structure. The hybrid approach ensures optimal performance and user experience.