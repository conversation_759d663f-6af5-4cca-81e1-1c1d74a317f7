"use client";

import React from "react";
import { UserAvatarMenu } from "./user-avatar-menu";
import { Menu, AlertTriangle } from "lucide-react";
import { ModeToggle } from "./ModeToggle";
import type { User } from "@supabase/supabase-js";
import type { Organization } from "@/types/organization";
import { useAuthContextStore } from "@/stores/useAuthContextStore";
import { useTranslations } from 'next-intl';

interface TopBarProps {
  isSidebarCollapsed: boolean;
  toggleSidebar: () => void;
  user: User | null;
  activeOrganization: Organization | null;
}

export function TopBar({ isSidebarCollapsed, toggleSidebar, user, activeOrganization }: TopBarProps) {
  // Read the isActiveOrgDisabled flag and loading state from context
  const isActiveOrgDisabled = useAuthContextStore(state => state.isActiveOrgDisabled);
  const isLoading = useAuthContextStore(state => state.isLoading);

  return (
    <header
      className={`bg-white border-gray-200 dark:bg-[#0A2C35] border-b border-[#e2e8ff1a] h-16 fixed top-0 right-0 left-0 z-50 transition-all duration-300 ${
        isSidebarCollapsed ? "lg:left-16" : "lg:left-64"
      }`}
    >
      <div className="px-6 h-full flex items-center justify-between">
        <button
          onClick={toggleSidebar}
          className="p-2 rounded-md hover:bg-gray-100 dark:hover:bg-gray-800"
        >
          <Menu className="w-6 h-6" />
        </button>

        {/* Only show organization status after hydration completes */}
        {!isLoading && isActiveOrgDisabled && (
          <div className="flex items-center bg-red-600/20 px-3 py-1 rounded-md">
            <AlertTriangle className="w-4 h-4 text-red-400 mr-2" />
            <span className="text-sm text-red-400">
              {activeOrganization ? `"${activeOrganization.name}"` : "Organization"} is disabled
            </span>
          </div>
        )}

        <div className="flex-1" />
        <div className="mr-6">
          <ModeToggle />
        </div>
        <UserAvatarMenu user={user} />
      </div>
    </header>
  );
}
