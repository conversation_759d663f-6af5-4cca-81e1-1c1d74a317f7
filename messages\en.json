{"Common": {"buttons": {"save": "Save", "cancel": "Cancel", "delete": "Delete", "edit": "Edit", "submit": "Submit", "loading": "Loading...", "confirm": "Confirm", "create": "Create", "update": "Update", "remove": "Remove", "search": "Search", "filter": "Filter", "close": "Close", "open": "Open"}, "actions": {"create": "Create", "update": "Update", "remove": "Remove", "search": "Search", "filter": "Filter", "sort": "Sort", "export": "Export", "import": "Import"}, "status": {"active": "Active", "inactive": "Inactive", "pending": "Pending", "disabled": "Disabled", "enabled": "Enabled", "loading": "Loading", "success": "Success", "error": "Error"}, "navigation": {"next": "Next", "previous": "Previous", "home": "Home", "back": "Back", "forward": "Forward"}}, "Tables": {"headers": {"name": "Name", "email": "Email", "role": "Role", "status": "Status", "actions": "Actions", "createdAt": "Created", "updatedAt": "Updated", "id": "ID"}, "pagination": {"showing": "Showing {start} to {end} of {total} results", "rowsPerPage": "Rows per page", "noData": "No data available", "page": "Page", "of": "of"}}, "Forms": {"validation": {"required": "This field is required", "email": "Please enter a valid email", "minLength": "Must be at least {min} characters", "maxLength": "Must be no more than {max} characters", "passwordMismatch": "Passwords do not match", "invalidFormat": "Invalid format"}, "labels": {"firstName": "First Name", "lastName": "Last Name", "email": "Email Address", "password": "Password", "confirmPassword": "Confirm Password", "name": "Name", "description": "Description"}, "placeholders": {"email": "<EMAIL>", "search": "Search...", "enterText": "Enter text..."}}, "Errors": {"general": "An error occurred", "network": "Network error occurred", "unauthorized": "You are not authorized to perform this action", "notFound": "The requested resource was not found", "serverError": "Internal server error", "validationFailed": "Validation failed"}, "Language": {"switchLanguage": "Switch Language", "currentLanguage": "Current Language"}}